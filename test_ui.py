#!/usr/bin/env python3
"""
Simple test script to verify UI components work.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rich.console import Console
from src.arien_cli.ui.components.header import HeaderComponent
from src.arien_cli.ui.components.chat_input import ChatInputComponent
from src.arien_cli.ui.components.chat_output import ChatOutputComponent
from src.arien_cli.ui.components.thinking_spinner import ThinkingSpinnerComponent
from src.arien_cli.ui.components.terminal_layout import TerminalLayoutComponent

def test_components():
    """Test individual UI components."""
    console = Console()
    
    console.print("[bold cyan]Testing Arien CLI UI Components[/bold cyan]")
    console.print()
    
    # Test Header Component
    console.print("[bold]1. Testing Header Component[/bold]")
    header = HeaderComponent()
    header.update(
        session_id="test-session-123",
        provider="deepseek",
        model="deepseek-chat",
        working_directory="/test/directory"
    )
    console.print(header.render())
    console.print()
    
    # Test Chat Input Component
    console.print("[bold]2. Testing Chat Input Component[/bold]")
    chat_input = ChatInputComponent()
    chat_input.set_input("Hello, this is a test message!")
    console.print(chat_input.render())
    console.print()
    
    # Test Chat Output Component
    console.print("[bold]3. Testing Chat Output Component[/bold]")
    chat_output = ChatOutputComponent()
    chat_output.add_message("user", "Hello, how are you?")
    chat_output.add_message("assistant", "I'm doing well! How can I help you today?")
    chat_output.add_message("system", "System message: Connection established")
    console.print(chat_output.render())
    console.print()
    
    # Test Thinking Spinner Component
    console.print("[bold]4. Testing Thinking Spinner Component[/bold]")
    spinner = ThinkingSpinnerComponent()
    spinner.start("Processing your request...")
    for i in range(5):
        console.print(spinner.render(), end="\r")
        import time
        time.sleep(0.5)
    spinner.stop()
    console.print()
    console.print()
    
    # Test Terminal Layout Component
    console.print("[bold]5. Testing Terminal Layout Component[/bold]")
    layout = TerminalLayoutComponent(console)
    layout.update_header(
        session_id="test-session-456",
        provider="deepseek",
        model="deepseek-chat"
    )
    layout.add_message("user", "Test message in layout")
    layout.add_message("assistant", "Response in layout")
    
    # Note: We can't easily test the full layout in this simple test
    console.print("✅ Terminal Layout Component initialized successfully")
    console.print()
    
    console.print("[bold green]✅ All UI components tested successfully![/bold green]")

def test_error_scenarios():
    """Test error handling scenarios."""
    console = Console()
    
    console.print("[bold cyan]Testing Error Scenarios[/bold cyan]")
    console.print()
    
    # Test chat output with errors
    chat_output = ChatOutputComponent()
    chat_output.add_error("Test error message", ["Try this", "Or try that"])
    console.print(chat_output.render())
    console.print()
    
    console.print("[bold green]✅ Error scenarios tested successfully![/bold green]")

if __name__ == "__main__":
    try:
        test_components()
        test_error_scenarios()
        print("\n🎉 All tests passed! The UI components are working correctly.")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
