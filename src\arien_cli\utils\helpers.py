"""
Helper utilities for Arien CLI.

This module provides various utility functions used throughout the application.
"""

import asyncio
import os
import platform
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import psutil


def get_system_info() -> Dict[str, Any]:
    """
    Get system information.
    
    Returns:
        Dictionary with system information
    """
    return {
        "platform": platform.system(),
        "platform_version": platform.version(),
        "architecture": platform.architecture()[0],
        "processor": platform.processor(),
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "memory_available": psutil.virtual_memory().available,
        "disk_usage": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:\\').percent,
    }


def get_shell_info() -> Dict[str, str]:
    """
    Get shell information.
    
    Returns:
        Dictionary with shell information
    """
    shell = os.environ.get('SHELL', '')
    if not shell and os.name == 'nt':
        shell = os.environ.get('COMSPEC', 'cmd.exe')
    
    return {
        "shell": shell,
        "shell_name": Path(shell).name if shell else "unknown",
        "terminal": os.environ.get('TERM', 'unknown'),
        "term_program": os.environ.get('TERM_PROGRAM', 'unknown'),
    }


def is_admin() -> bool:
    """
    Check if running with administrator/root privileges.
    
    Returns:
        True if running as admin/root
    """
    try:
        if os.name == 'nt':
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        else:
            return os.geteuid() == 0
    except Exception:
        return False


def get_available_ports(start_port: int = 8000, count: int = 10) -> List[int]:
    """
    Get list of available ports.
    
    Args:
        start_port: Starting port number
        count: Number of ports to check
        
    Returns:
        List of available port numbers
    """
    import socket
    
    available_ports = []
    
    for port in range(start_port, start_port + count * 10):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(('localhost', port))
                available_ports.append(port)
                if len(available_ports) >= count:
                    break
        except OSError:
            continue
    
    return available_ports


def format_bytes(bytes_value: int) -> str:
    """
    Format bytes into human-readable string.
    
    Args:
        bytes_value: Number of bytes
        
    Returns:
        Formatted string (e.g., "1.5 GB")
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted string (e.g., "2m 30s")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    
    minutes = int(seconds // 60)
    remaining_seconds = seconds % 60
    
    if minutes < 60:
        return f"{minutes}m {remaining_seconds:.1f}s"
    
    hours = int(minutes // 60)
    remaining_minutes = minutes % 60
    
    return f"{hours}h {remaining_minutes}m {remaining_seconds:.1f}s"


def safe_filename(filename: str) -> str:
    """
    Convert string to safe filename.
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename string
    """
    import re
    
    # Remove or replace unsafe characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    safe_name = re.sub(r'\s+', '_', safe_name)
    safe_name = safe_name.strip('._')
    
    # Ensure it's not empty
    if not safe_name:
        safe_name = "unnamed"
    
    # Limit length
    if len(safe_name) > 100:
        safe_name = safe_name[:100]
    
    return safe_name


def find_executable(name: str) -> Optional[str]:
    """
    Find executable in PATH.
    
    Args:
        name: Executable name
        
    Returns:
        Full path to executable or None if not found
    """
    import shutil
    return shutil.which(name)


def get_git_info(directory: str = ".") -> Dict[str, Optional[str]]:
    """
    Get git repository information.

    Args:
        directory: Directory to check

    Returns:
        Dictionary with git information
    """
    import subprocess

    git_info = {
        "branch": None,
        "commit": None,
        "remote": None,
        "status": None,
        "is_repo": False
    }

    try:
        # Check if directory exists
        if not os.path.exists(directory):
            return git_info

        # Check if it's a git repository
        subprocess.run(
            ["git", "rev-parse", "--git-dir"],
            cwd=directory,
            capture_output=True,
            check=True
        )
        git_info["is_repo"] = True
        
        # Get branch
        result = subprocess.run(
            ["git", "branch", "--show-current"],
            cwd=directory,
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            git_info["branch"] = result.stdout.strip()
        
        # Get commit
        result = subprocess.run(
            ["git", "rev-parse", "--short", "HEAD"],
            cwd=directory,
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            git_info["commit"] = result.stdout.strip()
        
        # Get remote
        result = subprocess.run(
            ["git", "remote", "get-url", "origin"],
            cwd=directory,
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            git_info["remote"] = result.stdout.strip()
        
        # Get status
        result = subprocess.run(
            ["git", "status", "--porcelain"],
            cwd=directory,
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            if result.stdout.strip():
                git_info["status"] = "dirty"
            else:
                git_info["status"] = "clean"
    
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    return git_info


def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if URL is valid
    """
    import re
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def get_terminal_size() -> Tuple[int, int]:
    """
    Get terminal size.
    
    Returns:
        Tuple of (width, height)
    """
    try:
        size = os.get_terminal_size()
        return size.columns, size.lines
    except OSError:
        return 80, 24  # Default size


def clear_screen() -> None:
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')


async def run_with_timeout(coro, timeout: float):
    """
    Run coroutine with timeout.
    
    Args:
        coro: Coroutine to run
        timeout: Timeout in seconds
        
    Returns:
        Coroutine result
        
    Raises:
        asyncio.TimeoutError: If timeout is exceeded
    """
    return await asyncio.wait_for(coro, timeout=timeout)


def get_config_dir() -> Path:
    """
    Get configuration directory for the application.
    
    Returns:
        Path to configuration directory
    """
    if os.name == 'nt':  # Windows
        config_dir = Path(os.environ.get('APPDATA', '')) / "arien-cli"
    else:  # Unix-like
        config_dir = Path.home() / ".config" / "arien-cli"
    
    config_dir.mkdir(parents=True, exist_ok=True)
    return config_dir


def get_cache_dir() -> Path:
    """
    Get cache directory for the application.
    
    Returns:
        Path to cache directory
    """
    if os.name == 'nt':  # Windows
        cache_dir = Path(os.environ.get('LOCALAPPDATA', '')) / "arien-cli" / "cache"
    else:  # Unix-like
        cache_dir = Path.home() / ".cache" / "arien-cli"
    
    cache_dir.mkdir(parents=True, exist_ok=True)
    return cache_dir


def get_logs_dir() -> Path:
    """
    Get logs directory for the application.
    
    Returns:
        Path to logs directory
    """
    if os.name == 'nt':  # Windows
        logs_dir = Path(os.environ.get('LOCALAPPDATA', '')) / "arien-cli" / "logs"
    else:  # Unix-like
        logs_dir = Path.home() / ".local" / "share" / "arien-cli" / "logs"
    
    logs_dir.mkdir(parents=True, exist_ok=True)
    return logs_dir
